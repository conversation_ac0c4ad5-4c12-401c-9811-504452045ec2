from manim import *

class AdultCeremonyEarringScene(Scene):
    def construct(self):
        # 设置画布尺寸为21:9
        self.camera.frame_width = 21
        self.camera.frame_height = 9

        # 1. 创建标题
        title = Text("成人礼与耳环争议", font_size=48).to_edge(UP)
        
        # 2. 加载人物图片
        try:
            character_image = ImageMobject("黄杨钿甜.jpg")
            character_image.set_height(3.6)  # 初始高度为画面40%
            character_image.move_to(LEFT * 4 + UP * 0.5)
        except FileNotFoundError:
            self.add(Text("错误：找不到黄杨钿甜.jpg", font_size=36, color=RED).to_edge(UP))
            self.wait(3)
            return

        # 3. 创建右侧文字
        ceremony_text = Text("成人礼", font_size=42).move_to(RIGHT * 4 + UP * 1.5)
        date_text = Text("2025.5.11", font_size=36).next_to(ceremony_text, DOWN)

        # 4. 创建底部滚动文字
        bottom_texts = [
            Text("05后演员黄杨钿甜", font_size=36),
            Text("成人礼活动", font_size=36),
            Text("GRAFF祖母绿钻石耳环", font_size=36),
            Text("引发网友关注", font_size=36)
        ]
        for text in bottom_texts:
            text.to_edge(DOWN, buff=0.5)

        # 5. 加载耳环图片
        try:
            earring_image = ImageMobject("黄杨钿甜佩戴的graff耳环.jpg")
            earring_image.set_height(4.5)  # 占画面50%高度
            earring_image.move_to(ORIGIN)
        except FileNotFoundError:
            self.add(Text("错误：找不到耳环图片", font_size=36, color=RED).to_edge(UP))
            self.wait(3)
            return

        # 6. 创建价格文字
        official_price = Text("官方公价：约230万元", font_size=42, color=RED)
        market_price = Text("市场价：15-60万元", font_size=42, color=GREEN)
        price_group = VGroup(official_price, market_price).arrange(RIGHT, buff=2)
        price_group.to_edge(DOWN, buff=1)

        # 动画序列
        # 1. 开场动画 (0-3秒)
        self.play(
            FadeIn(title, shift=UP),
            FadeIn(character_image, shift=RIGHT),
            run_time=1
        )
        self.play(
            Write(ceremony_text),
            Write(date_text),
            run_time=0.5
        )
        
        # 2. 底部文字动画
        for text in bottom_texts[:2]:
            self.play(
                text.animate.shift(LEFT * 20),
                run_time=0.3
            )
            self.wait(0.5)
            self.play(
                text.animate.shift(LEFT * 20),
                run_time=0.3
            )

        # 3. 人物图片缩小移动 (3-4秒)
        self.play(
            character_image.animate.scale(0.625).move_to(LEFT * 6 + UP * 2),
            run_time=1
        )

        # 4. 耳环出现 (4-5秒)
        self.play(
            FadeIn(earring_image, scale=1.1),
            run_time=1
        )

        # 5. 耳环强调动画 (5-6秒)
        self.play(
            earring_image.animate.scale(1.05),
            run_time=0.5
        )
        self.play(
            earring_image.animate.scale(1/1.05),
            run_time=0.5
        )

        # 6. 底部文字继续动画
        for text in bottom_texts[2:]:
            self.play(
                text.animate.shift(LEFT * 20),
                run_time=0.3
            )
            self.wait(0.5)
            self.play(
                text.animate.shift(LEFT * 20),
                run_time=0.3
            )

        # 7. 价格信息动画 (10-12秒)
        self.play(
            FadeIn(price_group, shift=UP),
            run_time=1
        )

        # 8. 结尾淡出 (14.5-15秒)
        self.wait(2.5)
        self.play(
            *[FadeOut(mob) for mob in self.mobjects],
            run_time=0.5
        )